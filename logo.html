<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Logo</title>
    <style>
      body {
        background-color: #fdfaf6;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
      }
    </style>
  </head>
  <body>
    <svg
      width="500"
      height="500"
      viewBox="0 0 500 500"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <!-- Enhanced gradients -->
        <linearGradient id="circleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#2E8B57" />
          <stop offset="50%" stop-color="#3FAA46" />
          <stop offset="100%" stop-color="#1E90FF" />
        </linearGradient>

        <linearGradient
          id="leftHandGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stop-color="#4CAF50" />
          <stop offset="100%" stop-color="#2E8B57" />
        </linearGradient>

        <linearGradient
          id="rightHandGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stop-color="#1E90FF" />
          <stop offset="100%" stop-color="#0066CC" />
        </linearGradient>

        <radialGradient id="centerGlow" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#FFD700" stop-opacity="0.8" />
          <stop offset="100%" stop-color="#FFA500" stop-opacity="0.3" />
        </radialGradient>

        <!-- Shadow filter -->
        <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow
            dx="2"
            dy="2"
            stdDeviation="3"
            flood-color="#000000"
            flood-opacity="0.3"
          />
        </filter>
      </defs>

      <!-- Outer decorative ring -->
      <circle
        cx="250"
        cy="220"
        r="120"
        stroke="url(#circleGradient)"
        stroke-width="3"
        fill="none"
        opacity="0.4"
      />

      <!-- Main circle with enhanced gradient -->
      <circle
        cx="250"
        cy="220"
        r="100"
        stroke="url(#circleGradient)"
        stroke-width="8"
        fill="none"
        filter="url(#dropShadow)"
      />

      <!-- Unique handshake design -->
      <g transform="translate(250,220)" filter="url(#dropShadow)">
        <!-- Connection glow effect -->
        <circle cx="0" cy="0" r="25" fill="url(#centerGlow)" opacity="0.6" />

        <!-- Left hand (more detailed and unique) -->
        <g transform="rotate(-15)">
          <!-- Left wrist -->
          <rect
            x="-45"
            y="-8"
            width="20"
            height="16"
            rx="8"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1"
          />

          <!-- Left palm -->
          <ellipse
            cx="-20"
            cy="0"
            rx="12"
            ry="18"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1.5"
          />

          <!-- Left thumb -->
          <ellipse
            cx="-25"
            cy="-12"
            rx="4"
            ry="8"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1"
            transform="rotate(-30 -25 -12)"
          />

          <!-- Left fingers -->
          <ellipse
            cx="-8"
            cy="-15"
            rx="3"
            ry="10"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1"
          />
          <ellipse
            cx="-4"
            cy="-17"
            rx="3"
            ry="12"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1"
          />
          <ellipse
            cx="0"
            cy="-16"
            rx="3"
            ry="11"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1"
          />
          <ellipse
            cx="4"
            cy="-14"
            rx="3"
            ry="9"
            fill="url(#leftHandGradient)"
            stroke="#2E8B57"
            stroke-width="1"
          />
        </g>

        <!-- Right hand (more detailed and unique) -->
        <g transform="rotate(15)">
          <!-- Right wrist -->
          <rect
            x="25"
            y="-8"
            width="20"
            height="16"
            rx="8"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1"
          />

          <!-- Right palm -->
          <ellipse
            cx="20"
            cy="0"
            rx="12"
            ry="18"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1.5"
          />

          <!-- Right thumb -->
          <ellipse
            cx="25"
            cy="-12"
            rx="4"
            ry="8"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1"
            transform="rotate(30 25 -12)"
          />

          <!-- Right fingers -->
          <ellipse
            cx="8"
            cy="-15"
            rx="3"
            ry="10"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1"
          />
          <ellipse
            cx="4"
            cy="-17"
            rx="3"
            ry="12"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1"
          />
          <ellipse
            cx="0"
            cy="-16"
            rx="3"
            ry="11"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1"
          />
          <ellipse
            cx="-4"
            cy="-14"
            rx="3"
            ry="9"
            fill="url(#rightHandGradient)"
            stroke="#0066CC"
            stroke-width="1"
          />
        </g>

        <!-- Central connection point with unique design -->
        <circle
          cx="0"
          cy="0"
          r="8"
          fill="#FFD700"
          stroke="#FFA500"
          stroke-width="2"
        />
        <circle cx="0" cy="0" r="4" fill="#FFFFFF" opacity="0.8" />

        <!-- Energy lines radiating from center -->
        <g stroke="#FFD700" stroke-width="2" opacity="0.6">
          <line x1="0" y1="-15" x2="0" y2="-25" />
          <line x1="0" y1="15" x2="0" y2="25" />
          <line x1="-15" y1="0" x2="-25" y2="0" />
          <line x1="15" y1="0" x2="25" y2="0" />
          <line x1="-11" y1="-11" x2="-18" y2="-18" />
          <line x1="11" y1="-11" x2="18" y2="-18" />
          <line x1="-11" y1="11" x2="-18" y2="18" />
          <line x1="11" y1="11" x2="18" y2="18" />
        </g>
      </g>
      <!-- Enhanced text gradient definition -->
      <defs>
        <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#2E8B57" />
          <stop offset="50%" stop-color="#3FAA46" />
          <stop offset="100%" stop-color="#1E90FF" />
        </linearGradient>
      </defs>

      <!-- Main text with enhanced styling -->
      <text
        x="250"
        y="410"
        font-family="Arial, sans-serif"
        font-size="28"
        font-weight="bold"
        fill="url(#textGradient)"
        text-anchor="middle"
        filter="url(#dropShadow)"
      >
        TRUSTED PEOPLE
      </text>
      <text
        x="250"
        y="440"
        font-family="Arial, sans-serif"
        font-size="28"
        font-weight="bold"
        fill="url(#textGradient)"
        text-anchor="middle"
        filter="url(#dropShadow)"
      >
        SMART SERVICES
      </text>

      <!-- Decorative underline -->
      <line
        x1="150"
        y1="450"
        x2="350"
        y2="450"
        stroke="url(#textGradient)"
        stroke-width="3"
        opacity="0.6"
      />
    </svg>
  </body>
</html>
