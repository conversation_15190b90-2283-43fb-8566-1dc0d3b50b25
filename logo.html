<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SERVIGO - Professional Logo</title>
    <style>
      body {
        background-color: #f8f9fa;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        font-family: 'Arial', sans-serif;
      }
      .logo-container {
        text-align: center;
        padding: 40px;
      }
    </style>
  </head>
  <body>
    <div class="logo-container">
      <!-- SERVIGO Professional Logo -->
      <svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <!-- Professional gradients -->
          <linearGradient id="outerRing" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#1565C0" />
            <stop offset="50%" stop-color="#2E7D32" />
            <stop offset="100%" stop-color="#1565C0" />
          </linearGradient>

          <linearGradient id="innerCircle" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#FFFFFF" />
            <stop offset="100%" stop-color="#F8F9FA" />
          </linearGradient>

          <!-- Seller hand gradient (Green) -->
          <linearGradient id="sellerHand" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#4CAF50" />
            <stop offset="100%" stop-color="#2E7D32" />
          </linearGradient>

          <!-- Buyer hand gradient (Blue) -->
          <linearGradient id="buyerHand" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#2196F3" />
            <stop offset="100%" stop-color="#1565C0" />
          </linearGradient>

          <!-- Connection point gradient -->
          <radialGradient id="connectionGlow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stop-color="#FFD700" />
            <stop offset="70%" stop-color="#FFA726" />
            <stop offset="100%" stop-color="#FF8F00" />
          </radialGradient>

          <!-- Text gradients -->
          <linearGradient id="servigoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#1565C0" />
            <stop offset="50%" stop-color="#2E7D32" />
            <stop offset="100%" stop-color="#1565C0" />
          </linearGradient>

          <linearGradient id="taglineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#424242" />
            <stop offset="100%" stop-color="#616161" />
          </linearGradient>

          <!-- Professional shadow filter -->
          <filter id="professionalShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.25"/>
          </filter>

          <!-- Inner shadow for depth -->
          <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="1" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.15"/>
          </filter>
        </defs>

        <!-- Outer professional ring -->
        <circle cx="300" cy="300" r="280"
                stroke="url(#outerRing)"
                stroke-width="12"
                fill="none"
                filter="url(#professionalShadow)" />

        <!-- Inner white circle background -->
        <circle cx="300" cy="300" r="250"
                fill="url(#innerCircle)"
                stroke="url(#outerRing)"
                stroke-width="4"
                filter="url(#innerShadow)" />

        <!-- Professional handshake design -->
        <g transform="translate(300,280)">
          <!-- Seller hand (left - green) -->
          <g transform="rotate(-20)">
            <!-- Seller wrist -->
            <rect x="-60" y="-12" width="25" height="20" rx="10"
                  fill="url(#sellerHand)"
                  stroke="#1B5E20"
                  stroke-width="2"/>

            <!-- Seller palm -->
            <ellipse cx="-30" cy="0" rx="15" ry="22"
                     fill="url(#sellerHand)"
                     stroke="#1B5E20"
                     stroke-width="2"/>

            <!-- Seller thumb -->
            <ellipse cx="-35" cy="-15" rx="5" ry="10"
                     fill="url(#sellerHand)"
                     stroke="#1B5E20"
                     stroke-width="1.5"
                     transform="rotate(-25 -35 -15)"/>

            <!-- Seller fingers -->
            <ellipse cx="-12" cy="-18" rx="4" ry="12" fill="url(#sellerHand)" stroke="#1B5E20" stroke-width="1"/>
            <ellipse cx="-7" cy="-20" rx="4" ry="14" fill="url(#sellerHand)" stroke="#1B5E20" stroke-width="1"/>
            <ellipse cx="-2" cy="-19" rx="4" ry="13" fill="url(#sellerHand)" stroke="#1B5E20" stroke-width="1"/>
            <ellipse cx="3" cy="-17" rx="4" ry="11" fill="url(#sellerHand)" stroke="#1B5E20" stroke-width="1"/>
          </g>

          <!-- Buyer hand (right - blue) -->
          <g transform="rotate(20)">
            <!-- Buyer wrist -->
            <rect x="35" y="-12" width="25" height="20" rx="10"
                  fill="url(#buyerHand)"
                  stroke="#0D47A1"
                  stroke-width="2"/>

            <!-- Buyer palm -->
            <ellipse cx="30" cy="0" rx="15" ry="22"
                     fill="url(#buyerHand)"
                     stroke="#0D47A1"
                     stroke-width="2"/>

            <!-- Buyer thumb -->
            <ellipse cx="35" cy="-15" rx="5" ry="10"
                     fill="url(#buyerHand)"
                     stroke="#0D47A1"
                     stroke-width="1.5"
                     transform="rotate(25 35 -15)"/>

            <!-- Buyer fingers -->
            <ellipse cx="12" cy="-18" rx="4" ry="12" fill="url(#buyerHand)" stroke="#0D47A1" stroke-width="1"/>
            <ellipse cx="7" cy="-20" rx="4" ry="14" fill="url(#buyerHand)" stroke="#0D47A1" stroke-width="1"/>
            <ellipse cx="2" cy="-19" rx="4" ry="13" fill="url(#buyerHand)" stroke="#0D47A1" stroke-width="1"/>
            <ellipse cx="-3" cy="-17" rx="4" ry="11" fill="url(#buyerHand)" stroke="#0D47A1" stroke-width="1"/>
          </g>

          <!-- Premium connection point -->
          <circle cx="0" cy="0" r="12"
                  fill="url(#connectionGlow)"
                  stroke="#E65100"
                  stroke-width="3"
                  filter="url(#professionalShadow)"/>
          <circle cx="0" cy="0" r="6" fill="#FFFFFF" opacity="0.9"/>
          <circle cx="0" cy="0" r="3" fill="url(#connectionGlow)"/>

          <!-- Professional energy lines -->
          <g stroke="url(#connectionGlow)" stroke-width="3" opacity="0.7">
            <line x1="0" y1="-20" x2="0" y2="-35" />
            <line x1="0" y1="20" x2="0" y2="35" />
            <line x1="-20" y1="0" x2="-35" y2="0" />
            <line x1="20" y1="0" x2="35" y2="0" />
            <line x1="-14" y1="-14" x2="-25" y2="-25" />
            <line x1="14" y1="-14" x2="25" y2="-25" />
            <line x1="-14" y1="14" x2="-25" y2="25" />
            <line x1="14" y1="14" x2="25" y2="25" />
          </g>
        </g>

        <!-- SERVIGO text integrated within circle (top arc) -->
        <path id="topArc" d="M 120 300 A 180 180 0 0 1 480 300"
              fill="none" stroke="none"/>
        <text font-family="Arial, sans-serif"
              font-size="42"
              font-weight="bold"
              fill="url(#servigoGradient)"
              filter="url(#professionalShadow)">
          <textPath href="#topArc" startOffset="50%" text-anchor="middle">
            SERVIGO
          </textPath>
        </text>

        <!-- Tagline integrated within circle (bottom arc) -->
        <path id="bottomArc" d="M 480 300 A 180 180 0 0 1 120 300"
              fill="none" stroke="none"/>
        <text font-family="Arial, sans-serif"
              font-size="18"
              font-weight="normal"
              fill="url(#taglineGradient)"
              filter="url(#innerShadow)">
          <textPath href="#bottomArc" startOffset="50%" text-anchor="middle">
            TRUSTED PEOPLE • SMART SERVICES
          </textPath>
        </text>

        <!-- Decorative elements -->
        <g transform="translate(300,300)" opacity="0.3">
          <!-- Small decorative dots around the circle -->
          <circle cx="0" cy="-220" r="4" fill="url(#servigoGradient)"/>
          <circle cx="156" cy="-156" r="3" fill="url(#servigoGradient)"/>
          <circle cx="220" cy="0" r="4" fill="url(#servigoGradient)"/>
          <circle cx="156" cy="156" r="3" fill="url(#servigoGradient)"/>
          <circle cx="0" cy="220" r="4" fill="url(#servigoGradient)"/>
          <circle cx="-156" cy="156" r="3" fill="url(#servigoGradient)"/>
          <circle cx="-220" cy="0" r="4" fill="url(#servigoGradient)"/>
          <circle cx="-156" cy="-156" r="3" fill="url(#servigoGradient)"/>
        </g>
      </svg>
    </div>
  </body>
</html>
  </body>
</html>
